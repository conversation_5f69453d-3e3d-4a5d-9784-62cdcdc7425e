#!/usr/bin/env python3
"""
训练初始化测试脚本

测试训练流程的初始化部分，确保所有配置修复都能正常工作
"""

import sys
import torch
from pathlib import Path

def test_model_initialization():
    """测试模型初始化"""
    print("=== 测试模型初始化 ===")
    
    try:
        from src.utils.config.loader import ConfigLoader
        from src.models.gan.gan_model import GANModel
        
        # 加载配置
        loader = ConfigLoader()
        config = loader.load_from_yaml('config.yaml')
        print("✅ 配置加载成功")
        
        # 测试模型初始化（使用小的特征维度避免内存问题）
        feature_dim = 10  # 使用小的特征维度进行测试
        window_size = 5   # 使用小的窗口大小
        
        print(f"尝试初始化GAN模型: feature_dim={feature_dim}, window_size={window_size}")
        
        # 这里我们只测试到模型创建，不进行完整训练
        model = GANModel(config, feature_dim=feature_dim, window_size=window_size)
        print("✅ GAN模型初始化成功")
        
        # 验证生成器配置应用
        gen_config = config.model.generator
        print(f"生成器激活函数: {gen_config.activation_type}")
        print(f"生成器层归一化: {gen_config.use_layer_norm}")
        print(f"生成器残差连接: {gen_config.use_residual}")
        
        # 验证判别器配置应用
        disc_config = config.model.discriminator
        print(f"判别器层数: {disc_config.num_layers}")
        print(f"判别器注意力类型: {disc_config.attention_types}")
        
        print("✅ 模型初始化测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 模型初始化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_generator_components():
    """测试生成器组件"""
    print("\n=== 测试生成器组件 ===")
    
    try:
        from src.utils.config.loader import ConfigLoader
        from src.models.gan.generator import TimeSeriesGenerator
        
        # 加载配置
        loader = ConfigLoader()
        config = loader.load_from_yaml('config.yaml')
        
        # 创建生成器
        feature_dim = 10
        noise_dim = config.model.noise_params.dim
        
        generator = TimeSeriesGenerator(
            feature_dim=feature_dim,
            noise_dim=noise_dim,
            config=config.model.generator,
            feature_extractor_config=config.model.feature_extractor,
            attention_config=config.model.attention
        )
        print("✅ 生成器创建成功")
        
        # 测试前向传播
        batch_size = 2
        sequence_length = 5
        
        noise = torch.randn(batch_size, sequence_length, noise_dim)
        features = torch.randn(batch_size, sequence_length, feature_dim)
        
        output = generator(noise, features)
        print(f"✅ 生成器前向传播成功: {noise.shape} + {features.shape} -> {output.shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ 生成器组件测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_discriminator_components():
    """测试判别器组件"""
    print("\n=== 测试判别器组件 ===")
    
    try:
        from src.utils.config.loader import ConfigLoader
        from src.models.gan.discriminator import TimeSeriesDiscriminator
        
        # 加载配置
        loader = ConfigLoader()
        config = loader.load_from_yaml('config.yaml')
        
        # 创建判别器
        input_features = 10
        
        discriminator = TimeSeriesDiscriminator(
            input_features=input_features,
            config=config.model.discriminator,
            attention_config=config.model.attention
        )
        print("✅ 判别器创建成功")
        
        # 测试前向传播
        batch_size = 2
        sequence_length = 5
        
        x = torch.randn(batch_size, sequence_length, input_features)
        output = discriminator(x)
        print(f"✅ 判别器前向传播成功: {x.shape} -> {output.shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ 判别器组件测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🚀 开始训练初始化测试")
    print("=" * 60)
    
    tests = [
        ("模型初始化", test_model_initialization),
        ("生成器组件", test_generator_components),
        ("判别器组件", test_discriminator_components),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}")
        try:
            success = test_func()
            results.append(success)
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append(False)
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📊 测试结果")
    print("=" * 60)
    
    for i, (test_name, _) in enumerate(tests):
        status = "✅ 通过" if results[i] else "❌ 失败"
        print(f"{test_name}: {status}")
    
    all_passed = all(results)
    passed_count = sum(results)
    total_count = len(results)
    
    print(f"\n总体结果: {passed_count}/{total_count} 测试通过")
    
    if all_passed:
        print("🎉 所有训练初始化测试通过！")
        print("✅ 配置修复完全成功，系统可以正常训练")
    else:
        print("⚠️  部分测试失败，需要进一步检查")
    
    return all_passed


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
