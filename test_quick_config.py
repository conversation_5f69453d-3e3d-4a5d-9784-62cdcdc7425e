#!/usr/bin/env python3
"""
快速配置测试脚本

验证配置修复是否成功，包括：
1. 激活函数工厂测试
2. 配置加载测试
3. 生成器和判别器配置对象化测试
"""

import sys
import torch
from pathlib import Path

def test_activation_factory():
    """测试激活函数工厂"""
    print("=== 测试激活函数工厂 ===")
    
    try:
        from src.models.gan.components.activation_factory import create_activation, test_all_activations
        
        # 测试Swish激活函数
        swish = create_activation("swish")
        print(f"✅ 成功创建Swish激活函数: {type(swish).__name__}")
        
        # 测试前向传播
        x = torch.tensor([0.0, 1.0, -1.0, 2.0])
        y = swish(x)
        print(f"✅ Swish前向传播: {x.tolist()} -> {y.tolist()}")
        
        # 验证Swish数学性质
        expected = x * torch.sigmoid(x)
        if torch.allclose(y, expected, atol=1e-6):
            print("✅ Swish数学验证通过: f(x) = x * sigmoid(x)")
        else:
            print("❌ Swish数学验证失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 激活函数工厂测试失败: {e}")
        return False


def test_config_loading():
    """测试配置加载"""
    print("\n=== 测试配置加载 ===")
    
    try:
        from src.utils.config.loader import ConfigLoader
        
        loader = ConfigLoader()
        config = loader.load_from_yaml('config.yaml')
        print("✅ 配置文件加载成功")
        
        # 测试生成器配置
        gen_config = config.model.generator
        print(f"生成器配置类型: {type(gen_config).__name__}")
        print(f"激活函数: {gen_config.activation_type}")
        print(f"层归一化: {gen_config.use_layer_norm}")
        print(f"残差连接: {gen_config.use_residual}")
        print(f"噪声注入层: {gen_config.noise_injection_layers}")
        
        # 测试判别器配置
        disc_config = config.model.discriminator
        print(f"判别器配置类型: {type(disc_config).__name__}")
        print(f"层数: {disc_config.num_layers}")
        print(f"隐藏维度: {disc_config.hidden_dims}")
        print(f"注意力类型: {disc_config.attention_types}")
        
        # 验证关键配置值
        assert gen_config.activation_type == "swish"
        assert gen_config.use_layer_norm == False
        assert gen_config.use_residual == True
        assert gen_config.noise_injection_layers == [4]
        assert disc_config.num_layers == 6
        assert disc_config.attention_types == ["adaptive_dilation", "causal"]
        
        print("✅ 配置验证全部通过")
        return True
        
    except Exception as e:
        print(f"❌ 配置加载测试失败: {e}")
        return False


def test_weight_initialization():
    """测试权重初始化"""
    print("\n=== 测试权重初始化 ===")
    
    try:
        from src.models.gan.components.activation_factory import create_activation
        from src.models.base.base_module import BaseModule
        
        class SimpleTestModule(BaseModule):
            def __init__(self):
                super().__init__("SimpleTestModule")
                self.swish = create_activation("swish")
                self.linear = torch.nn.Linear(5, 3)
        
        module = SimpleTestModule()
        print("✅ 包含Swish的模块初始化成功")
        
        # 测试前向传播
        x = torch.randn(2, 5)
        y = module.linear(module.swish(x))
        print(f"✅ 前向传播成功: {x.shape} -> {y.shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ 权重初始化测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始快速配置测试")
    print("=" * 50)
    
    tests = [
        ("激活函数工厂", test_activation_factory),
        ("配置加载", test_config_loading),
        ("权重初始化", test_weight_initialization),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}")
        try:
            success = test_func()
            results.append(success)
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append(False)
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("📊 测试结果")
    print("=" * 50)
    
    for i, (test_name, _) in enumerate(tests):
        status = "✅ 通过" if results[i] else "❌ 失败"
        print(f"{test_name}: {status}")
    
    all_passed = all(results)
    passed_count = sum(results)
    total_count = len(results)
    
    print(f"\n总体结果: {passed_count}/{total_count} 测试通过")
    
    if all_passed:
        print("🎉 所有配置修复测试通过！")
        print("✅ 配置修复成功，可以继续训练")
    else:
        print("⚠️  部分测试失败，需要进一步检查")
    
    return all_passed


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
