"""模型配置模块 - 管理模型架构和参数相关配置"""

from __future__ import annotations

from dataclasses import dataclass
from typing import Any

from src.utils.config.training import LossConfig  # Ensure LossConfig is imported

# Removed duplicate import

# 移除 BaseConfig 继承，但保留 noise_dim 和 dimensions 作为必需字段
# @dataclass
# class BaseModelConfig(BaseConfig):

@dataclass
class NoiseParamsConfig:
    """模型中噪声参数的具体配置 (对应原 NoiseConfig)"""
    dim: int
    distribution: str
    scale: float
    seed: int | None # 允许为 None
    dtype: str  # 在 YAML 中通常为字符串，加载时转换为 torch.dtype
    structured: bool
    temporal_correlation: float
    feature_correlation: float
    noise_patterns: list[str]

    def __post_init__(self):
        from src.utils.logger import get_logger
        logger = get_logger(__name__)
        missing_fields = []
        if not hasattr(self, 'dim'): missing_fields.append('dim')
        if not hasattr(self, 'distribution'): missing_fields.append('distribution')
        if not hasattr(self, 'scale'): missing_fields.append('scale')
        # seed is optional
        if not hasattr(self, 'dtype'): missing_fields.append('dtype')
        if not hasattr(self, 'structured'): missing_fields.append('structured')
        if not hasattr(self, 'temporal_correlation'): missing_fields.append('temporal_correlation')
        if not hasattr(self, 'feature_correlation'): missing_fields.append('feature_correlation')
        if not hasattr(self, 'noise_patterns') or not isinstance(self.noise_patterns, list):
            missing_fields.append('noise_patterns (必须是列表)')

        if missing_fields:
            error_msg = f"NoiseParamsConfig 缺失或类型错误: {', '.join(missing_fields)}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        # Validate dtype string if needed, conversion to torch.dtype happens during NoiseManager init
        valid_dtypes = ["torch.float32", "torch.float16", "torch.bfloat16", "float32", "float16", "bfloat16"] # 示例
        if self.dtype not in valid_dtypes:
            # 允许简写形式，如 "float32"
            if not self.dtype.startswith("torch.") and f"torch.{self.dtype}" in valid_dtypes:
                pass # Allow "float32" as valid
            else:
                error_msg = f"无效的 dtype: {self.dtype}，有效选项: {valid_dtypes}"
                logger.error(error_msg)
                raise ValueError(error_msg)

        if not (0 <= self.temporal_correlation <= 1):
            raise ValueError(f"temporal_correlation 必须在 [0, 1] 之间, 得到 {self.temporal_correlation}")
        if not (0 <= self.feature_correlation <= 1):
            raise ValueError(f"feature_correlation 必须在 [0, 1] 之间, 得到 {self.feature_correlation}")
        if not self.noise_patterns:
            raise ValueError("noise_patterns 列表不能为空")


@dataclass
class BaseModelConfig:
    """基础模型配置类 (不再继承 BaseConfig)"""
    # 必需字段（没有默认值）
    type: str
    hidden_dim: int
    dropout_rate: float
    mixed_precision: dict[str, Any] # 假设混合精度配置是必需的
    loss: LossConfig # 假设 Loss 配置是必需的
    loss_type: str # loss_type 似乎与 loss 重复，但暂时保留以匹配现有代码
    noise: NoiseParamsConfig # 更新为 NoiseParamsConfig 类型
    n_heads: int # 添加 n_heads
    # 将 noise_dim 和 dimensions 添加回来作为必需字段
    noise_dim: int
    dimensions: dict[str, Any]
    # 可选字段（有默认值）- NAS 相关的配置属性
    generator: dict[str, Any] | None = None
    discriminator: dict[str, Any] | None = None
    attention: 'AttentionConfig | None' = None
    feature_extractor: 'FeatureExtractorConfig | None' = None

    def __post_init__(self):
        """初始化基础模型配置，检查必需字段并在缺失时抛出异常"""
        from src.utils.logger import get_logger
        logger = get_logger(__name__)

        # 检查必需字段
        missing_fields = []

        # 检查必需字段是否存在 (移除 is None)
        if not hasattr(self, 'type'): missing_fields.append('type')
        if not hasattr(self, 'hidden_dim'): missing_fields.append('hidden_dim')
        if not hasattr(self, 'dropout_rate'): missing_fields.append('dropout_rate')
        if not hasattr(self, 'mixed_precision'): missing_fields.append('mixed_precision')
        if not hasattr(self, 'loss') or not isinstance(self.loss, LossConfig):
             missing_fields.append('loss (必须是 LossConfig 类型)')
        if not hasattr(self, 'loss_type'): missing_fields.append('loss_type')
        if not hasattr(self, 'noise') or not isinstance(self.noise, NoiseParamsConfig):
             missing_fields.append('noise (必须是 NoiseParamsConfig 类型)')
        if not hasattr(self, 'n_heads'): missing_fields.append('n_heads')
        # 添加对 noise_dim 和 dimensions 的检查
        if not hasattr(self, 'noise_dim'): missing_fields.append('noise_dim')
        if not hasattr(self, 'dimensions') or not isinstance(self.dimensions, dict):
             missing_fields.append('dimensions (必须是字典类型)')

        # 如果有缺失字段，抛出异常
        if missing_fields:
            error_msg = f"基础模型配置缺失必需字段: {', '.join(missing_fields)}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        # 验证loss_type有效性
        valid_loss_types = ["mse", "mae", "huber"]
        if self.loss_type not in valid_loss_types:
            error_msg = f"无效的损失函数类型: {self.loss_type}，有效选项: {valid_loss_types}"
            logger.error(error_msg)
            raise ValueError(error_msg)

# 移除 BaseConfig 继承
# @dataclass
# class SequenceStrategyConfig(BaseConfig):
@dataclass
class SequenceStrategyConfig:
    """序列策略配置 (不再继承 BaseConfig)"""
    # 移除 Optional 和 = None，变为必需字段
    type: str
    window_size: int
    stride: int
    padding: str
    # 移除 dimensions 和 noise_dim

    def __post_init__(self):
        """初始化序列策略配置，检查必需字段并在缺失时抛出异常"""
        from src.utils.logger import get_logger
        logger = get_logger(__name__)

        # 检查必需字段
        missing_fields = []

        # 检查必需字段是否存在 (移除 is None)
        if not hasattr(self, 'type'): missing_fields.append('type')
        if not hasattr(self, 'window_size'): missing_fields.append('window_size')
        if not hasattr(self, 'stride'): missing_fields.append('stride')
        if not hasattr(self, 'padding'): missing_fields.append('padding')

        # 如果有缺失字段，抛出异常
        if missing_fields:
            error_msg = f"序列策略配置缺失必需字段: {', '.join(missing_fields)}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        # 验证类型
        valid_types = ["sliding_window", "fixed_window", "attention"]
        if self.type not in valid_types:
            error_msg = f"无效的序列策略类型: {self.type}，有效选项: {valid_types}"
            logger.error(error_msg)
            raise ValueError(error_msg)

# 移除DimensionAdapterConfig类，因为维度适配功能已经集成到生成器和判别器中

@dataclass
class AttentionConfig:
    """注意力机制特定配置"""
    multi_head_num_heads: int # 新增: MultiHeadAttention 的头数
    multi_head_dropout: float
    multi_scale_num_heads: int
    multi_scale_num_scales: int
    multi_scale_dropout: float
    multi_scale_dilation_rates: list[int]
    temporal_wrapper_dropout: float
    adaptive_attention_num_scales: int
    adaptive_attention_dropout: float
    adaptive_attention_num_heads: int # 新增: AdaptiveAttention 的头数

    def __post_init__(self):
        from src.utils.logger import get_logger
        logger = get_logger(__name__)
        missing_fields = []
        required_attention_fields = [
            'multi_head_num_heads', 'multi_head_dropout',
            'multi_scale_num_heads', 'multi_scale_num_scales',
            'multi_scale_dropout', 'multi_scale_dilation_rates',
            'temporal_wrapper_dropout', 'adaptive_attention_num_scales',
            'adaptive_attention_dropout', 'adaptive_attention_num_heads' # 新增字段
        ]
        for field_name in required_attention_fields:
            if not hasattr(self, field_name):
                missing_fields.append(field_name)

        if hasattr(self, 'multi_scale_dilation_rates') and not isinstance(self.multi_scale_dilation_rates, list):
            missing_fields.append('multi_scale_dilation_rates (必须是列表)')
        elif hasattr(self, 'multi_scale_dilation_rates') and isinstance(self.multi_scale_dilation_rates, list):
            if not self.multi_scale_dilation_rates: # 检查列表是否为空
                 missing_fields.append('multi_scale_dilation_rates (列表不能为空)')
            for rate in self.multi_scale_dilation_rates:
                 if not isinstance(rate, int) or rate <= 0:
                      missing_fields.append(f'multi_scale_dilation_rates (必须包含正整数, 得到 {rate})')

        if hasattr(self, 'multi_scale_num_scales') and hasattr(self, 'multi_scale_dilation_rates') and \
           isinstance(self.multi_scale_num_scales, int) and isinstance(self.multi_scale_dilation_rates, list) and \
           self.multi_scale_num_scales > 0 and self.multi_scale_dilation_rates and \
           len(self.multi_scale_dilation_rates) != self.multi_scale_num_scales:
            missing_fields.append(f"multi_scale_dilation_rates 列表长度 ({len(self.multi_scale_dilation_rates)}) 与 multi_scale_num_scales ({self.multi_scale_num_scales}) 不匹配")


        if hasattr(self, 'multi_head_num_heads') and (not isinstance(self.multi_head_num_heads, int) or self.multi_head_num_heads <= 0):
            missing_fields.append('multi_head_num_heads (必须是正整数)')
        if hasattr(self, 'multi_head_dropout') and (not isinstance(self.multi_head_dropout, (float, int)) or not (0 <= self.multi_head_dropout < 1)):
            missing_fields.append('multi_head_dropout (必须在 [0, 1) 范围内)')
        if hasattr(self, 'multi_scale_num_heads') and (not isinstance(self.multi_scale_num_heads, int) or self.multi_scale_num_heads <= 0):
            missing_fields.append('multi_scale_num_heads (必须是正整数)')
        if hasattr(self, 'multi_scale_num_scales') and (not isinstance(self.multi_scale_num_scales, int) or self.multi_scale_num_scales <= 0):
            missing_fields.append('multi_scale_num_scales (必须是正整数)')
        if hasattr(self, 'multi_scale_dropout') and (not isinstance(self.multi_scale_dropout, (float, int)) or not (0 <= self.multi_scale_dropout < 1)):
            missing_fields.append('multi_scale_dropout (必须在 [0, 1) 范围内)')

        # 新增字段的验证
        if hasattr(self, 'temporal_wrapper_dropout') and \
           (not isinstance(self.temporal_wrapper_dropout, (float, int)) or not (0 <= self.temporal_wrapper_dropout < 1)):
            missing_fields.append('temporal_wrapper_dropout (必须在 [0, 1) 范围内)')

        if hasattr(self, 'adaptive_attention_num_scales') and \
           (not isinstance(self.adaptive_attention_num_scales, int) or self.adaptive_attention_num_scales <= 0):
            missing_fields.append('adaptive_attention_num_scales (必须是正整数)')

        if hasattr(self, 'adaptive_attention_dropout') and \
           (not isinstance(self.adaptive_attention_dropout, (float, int)) or not (0 <= self.adaptive_attention_dropout < 1)):
            missing_fields.append('adaptive_attention_dropout (必须在 [0, 1) 范围内)')

        if hasattr(self, 'adaptive_attention_num_heads') and \
           (not isinstance(self.adaptive_attention_num_heads, int) or self.adaptive_attention_num_heads <= 0):
            missing_fields.append('adaptive_attention_num_heads (必须是正整数)')

        if missing_fields:
            # 去重，以防一个字段因多种原因被添加多次
            unique_missing_fields = sorted(list(set(missing_fields)))
            error_msg = f"AttentionConfig 缺失或类型错误: {', '.join(unique_missing_fields)}"
            logger.error(error_msg)
            raise ValueError(error_msg)

@dataclass
class FeatureExtractorConfig:
    """特征提取器配置"""
    msfe_num_scales: int
    msfe_dropout: float
    msfe_kernel_sizes: list[int]
    tsfe_num_layers: int
    tsfe_dropout: float
    tsfe_hidden_dim: int
    msfe_input_dim: int  # 新增
    msfe_hidden_dim: int # 新增
    tsfe_input_dim: int  # 新增
    tsfe_output_dim: int # 新增

    def __post_init__(self):
        from src.utils.logger import get_logger
        logger = get_logger(__name__)
        missing_fields = []
        required_fields = [
            'msfe_num_scales', 'msfe_dropout', 'msfe_kernel_sizes',
            'tsfe_num_layers', 'tsfe_dropout', 'tsfe_hidden_dim',
            'msfe_input_dim', 'msfe_hidden_dim', 'tsfe_input_dim', 'tsfe_output_dim'  # 新增
        ]
        for field_name in required_fields:
            if not hasattr(self, field_name):
                missing_fields.append(field_name)

        if hasattr(self, 'msfe_kernel_sizes') and not isinstance(self.msfe_kernel_sizes, list):
            missing_fields.append('msfe_kernel_sizes (必须是列表)')
        elif hasattr(self, 'msfe_kernel_sizes') and isinstance(self.msfe_kernel_sizes, list):
            if not self.msfe_kernel_sizes:
                 missing_fields.append('msfe_kernel_sizes (列表不能为空)')
            for size in self.msfe_kernel_sizes:
                 if not isinstance(size, int) or size <= 0:
                       missing_fields.append(f'msfe_kernel_sizes (必须包含正整数, 得到 {size})')
            if hasattr(self, 'msfe_num_scales') and isinstance(self.msfe_num_scales, int) and \
               len(self.msfe_kernel_sizes) != self.msfe_num_scales:
                missing_fields.append(f"msfe_kernel_sizes 列表长度 ({len(self.msfe_kernel_sizes)}) 与 msfe_num_scales ({self.msfe_num_scales}) 不匹配")


        if hasattr(self, 'msfe_num_scales') and (not isinstance(self.msfe_num_scales, int) or self.msfe_num_scales <= 0):
            missing_fields.append('msfe_num_scales (必须是正整数)')
        if hasattr(self, 'msfe_dropout') and (not isinstance(self.msfe_dropout, (float, int)) or not (0 <= self.msfe_dropout < 1)):
            missing_fields.append('msfe_dropout (必须在 [0, 1) 范围内)')
        if hasattr(self, 'tsfe_num_layers') and (not isinstance(self.tsfe_num_layers, int) or self.tsfe_num_layers <= 0):
            missing_fields.append('tsfe_num_layers (必须是正整数)')
        if hasattr(self, 'tsfe_dropout') and (not isinstance(self.tsfe_dropout, (float, int)) or not (0 <= self.tsfe_dropout < 1)):
            missing_fields.append('tsfe_dropout (必须在 [0, 1) 范围内)')
        if hasattr(self, 'tsfe_hidden_dim') and (not isinstance(self.tsfe_hidden_dim, int) or self.tsfe_hidden_dim <= 0):
            missing_fields.append('tsfe_hidden_dim (必须是正整数)')
        if hasattr(self, 'msfe_input_dim') and (not isinstance(self.msfe_input_dim, int) or self.msfe_input_dim <= 0):
            missing_fields.append('msfe_input_dim (必须是正整数)')
        if hasattr(self, 'msfe_hidden_dim') and (not isinstance(self.msfe_hidden_dim, int) or self.msfe_hidden_dim <= 0):
            missing_fields.append('msfe_hidden_dim (必须是正整数)')
        if hasattr(self, 'tsfe_input_dim') and (not isinstance(self.tsfe_input_dim, int) or self.tsfe_input_dim <= 0):
            missing_fields.append('tsfe_input_dim (必须是正整数)')
        if hasattr(self, 'tsfe_output_dim') and (not isinstance(self.tsfe_output_dim, int) or self.tsfe_output_dim <= 0):
            missing_fields.append('tsfe_output_dim (必须是正整数)')

        if missing_fields:
            unique_missing_fields = sorted(list(set(missing_fields)))
            error_msg = f"FeatureExtractorConfig 缺失或类型错误: {', '.join(unique_missing_fields)}"
            logger.error(error_msg)
            raise ValueError(error_msg)


@dataclass
class GANModelConfig(BaseModelConfig):
    """GAN模型配置"""
    # 新增字段，由于基类有默认值字段，这里也必须有默认值
    generator_type: str = "lstm"
    num_layers: int = 2
    use_spectral_norm: bool = True
    use_batch_norm: bool = True
    activation: str = "relu"
    sequence_strategy: SequenceStrategyConfig | None = None
    # 注意：generator, discriminator, attention, feature_extractor 从基类继承，这里不重新定义

    def __post_init__(self):
        # 不再调用 super().__post_init__() 因为基类不再是 BaseConfig
        # super().__post_init__()

        from src.utils.logger import get_logger
        logger = get_logger(__name__)

        # 首先调用基类的验证
        super().__post_init__()

        # 检查GANModelConfig特有的字段
        missing_fields = []
        required_fields = [
            'generator_type', 'num_layers', 'use_spectral_norm',
            'use_batch_norm', 'activation'
        ]
        for field_name in required_fields:
            if not hasattr(self, field_name):
                 missing_fields.append(field_name)

        # 特殊检查嵌套类型（可选字段）
        if hasattr(self, 'sequence_strategy') and self.sequence_strategy is not None and not isinstance(self.sequence_strategy, SequenceStrategyConfig):
             missing_fields.append('sequence_strategy (必须是 SequenceStrategyConfig 类型或 None)')

        # 如果有缺失字段，抛出异常
        if missing_fields:
            error_msg = f"GAN模型配置缺失或类型错误: {', '.join(missing_fields)}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        # 移除 loss 的回退/默认创建逻辑
        # loss 字段现在是必需的 LossConfig 类型，由 loader 创建
        # if isinstance(self.loss, dict):
        #     ... (移除转换逻辑) ...
        # elif self.loss is None:
        #     ... (移除默认创建逻辑) ...

        # 验证激活函数
        valid_activations = ["relu", "leaky_relu", "tanh", "sigmoid", "elu", "gelu"]
        if self.activation not in valid_activations:
            error_msg = f"无效的激活函数: {self.activation}，有效选项: {valid_activations}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        # 验证生成器类型
        valid_generator_types = ["lstm", "gru", "transformer", "cnn", "mlp"]
        if self.generator_type not in valid_generator_types:
            error_msg = f"无效的生成器类型: {self.generator_type}，有效选项: {valid_generator_types}"
            logger.error(error_msg)
            raise ValueError(error_msg)

# 移除 BaseConfig 继承
# @dataclass
# class GeneratorConfig(BaseConfig):
@dataclass
class GeneratorConfig:
    """生成器配置 (不再继承 BaseConfig)"""
    # 移除 Optional 和 = None，变为必需字段
    num_layers: int
    hidden_dim: int
    learning_rate: float
    beta1: float
    beta2: float
    grad_clip: float
    ema_decay: float
    # 移除 dimensions 和 noise_dim

    def __post_init__(self):
        """初始化生成器配置，检查必需字段并在缺失时抛出异常"""
        from src.utils.logger import get_logger
        logger = get_logger(__name__)

        # 检查必需字段
        missing_fields = []

        # 检查必需字段是否存在 (移除 is None)
        if not hasattr(self, 'num_layers'): missing_fields.append('num_layers')
        if not hasattr(self, 'hidden_dim'): missing_fields.append('hidden_dim')
        if not hasattr(self, 'learning_rate'): missing_fields.append('learning_rate')
        if not hasattr(self, 'beta1'): missing_fields.append('beta1')
        if not hasattr(self, 'beta2'): missing_fields.append('beta2')
        if not hasattr(self, 'grad_clip'): missing_fields.append('grad_clip')
        if not hasattr(self, 'ema_decay'): missing_fields.append('ema_decay')

        # 如果有缺失字段，抛出异常
        if missing_fields:
            error_msg = f"生成器配置缺失必需字段: {', '.join(missing_fields)}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        # 验证参数有效性
        # 移除断言，因为字段不再是 Optional
        # assert self.num_layers is not None, "num_layers不能为None"
        # ... (移除其他断言) ...

        if int(self.num_layers) <= 0:
            error_msg = f"num_layers必须大于0，但获取到的是: {self.num_layers}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        if int(self.hidden_dim) <= 0:
            error_msg = f"hidden_dim必须大于0，但获取到的是: {self.hidden_dim}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        if float(self.learning_rate) <= 0:
            error_msg = f"learning_rate必须大于0，但获取到的是: {self.learning_rate}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        beta1_float = float(self.beta1)
        if not 0 <= beta1_float < 1:
            error_msg = f"beta1必须在[0, 1)范围内，但获取到的是: {self.beta1}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        beta2_float = float(self.beta2)
        if not 0 <= beta2_float < 1:
            error_msg = f"beta2必须在[0, 1)范围内，但获取到的是: {self.beta2}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        if float(self.grad_clip) < 0:
            error_msg = f"grad_clip必须大于等于0，但获取到的是: {self.grad_clip}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        ema_decay_float = float(self.ema_decay)
        if not 0 <= ema_decay_float <= 1:
            error_msg = f"ema_decay必须在[0, 1]范围内，但获取到的是: {self.ema_decay}"
            logger.error(error_msg)
            raise ValueError(error_msg)

# 移除 BaseConfig 继承
# @dataclass
# class DiscriminatorConfig(BaseConfig):
@dataclass
class DiscriminatorConfig:
    """判别器配置 (不再继承 BaseConfig)"""
    # 移除 Optional 和 = None，变为必需字段
    num_layers: int
    hidden_dim: int
    learning_rate: float
    beta1: float
    beta2: float
    hidden_dims: list[int] # 变为必需
    # 新增的注意力机制开关 (必需字段) - 移到 input_features 之前
    enable_dynamic_fusion: bool
    enable_temporal_attention: bool
    enable_adaptive_dilation_attention: bool
    enable_multiscale_convolution_attention: bool
    # NAS优化新增参数 (可选参数，有默认值)
    num_attention_heads: int = 4  # NAS优化：注意力头数
    attention_types: list[str] | None = None  # NAS优化：注意力类型列表
    use_spectral_norm: bool = True  # NAS优化：是否使用谱归一化
    weight_sharing: bool = False  # NAS优化：分支间是否共享权重
    branch_config: dict[str, bool] | None = None  # NAS优化：分支配置
    input_features: int | None = None # 改为可选参数，并移到所有无默认值参数之后
    # 移除 dimensions 和 noise_dim

    def __post_init__(self):
        """初始化判别器配置，检查必需字段并在缺失时抛出异常"""
        from src.utils.logger import get_logger
        logger = get_logger(__name__)

        # 设置NAS优化参数的默认值
        if self.attention_types is None:
            self.attention_types = ["temporal_wrapper"]

        if self.branch_config is None:
            self.branch_config = {
                "trend_branch": True,
                "feature_branch": True,
                "temporal_branch": True
            }

        # 检查必需字段
        missing_fields = []

        # 检查必需字段是否存在 (移除 is None)
        if not hasattr(self, 'num_layers'): missing_fields.append('num_layers')
        if not hasattr(self, 'hidden_dim'): missing_fields.append('hidden_dim')
        if not hasattr(self, 'learning_rate'): missing_fields.append('learning_rate')
        if not hasattr(self, 'beta1'): missing_fields.append('beta1')
        if not hasattr(self, 'beta2'): missing_fields.append('beta2')
        # input_features 现在是可选的，但如果提供了，则需要验证其有效性
        # if not hasattr(self, 'input_features'): missing_fields.append('input_features')
        if not hasattr(self, 'hidden_dims') or not isinstance(self.hidden_dims, list): # 检查 hidden_dims
             missing_fields.append('hidden_dims (必须是列表)')
        # 添加对新开关的检查
        if not hasattr(self, 'enable_dynamic_fusion'): missing_fields.append('enable_dynamic_fusion')
        if not hasattr(self, 'enable_temporal_attention'): missing_fields.append('enable_temporal_attention')
        if not hasattr(self, 'enable_adaptive_dilation_attention'): missing_fields.append('enable_adaptive_dilation_attention')
        if not hasattr(self, 'enable_multiscale_convolution_attention'): missing_fields.append('enable_multiscale_convolution_attention')

        # 如果有缺失字段，抛出异常
        if missing_fields:
            error_msg = f"判别器配置缺失必需字段: {', '.join(missing_fields)}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        # 验证参数有效性
        # 移除断言，因为字段不再是 Optional
        # assert self.num_layers is not None, "num_layers不能为None"
        # ... (移除其他断言) ...

        if int(self.num_layers) <= 0:
            error_msg = f"num_layers必须大于0，但获取到的是: {self.num_layers}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        if int(self.hidden_dim) <= 0:
            error_msg = f"hidden_dim必须大于0，但获取到的是: {self.hidden_dim}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        if float(self.learning_rate) <= 0:
            error_msg = f"learning_rate必须大于0，但获取到的是: {self.learning_rate}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        beta1_float = float(self.beta1)
        if not 0 <= beta1_float < 1:
            error_msg = f"beta1必须在[0, 1)范围内，但获取到的是: {self.beta1}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        beta2_float = float(self.beta2)
        if not 0 <= beta2_float < 1:
            error_msg = f"beta2必须在[0, 1)范围内，但获取到的是: {self.beta2}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        # 如果 input_features 提供了，则验证它
        if self.input_features is not None and int(self.input_features) <= 0:
            error_msg = f"如果提供了 input_features，则必须大于0，但获取到的是: {self.input_features}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        # 移除 hidden_dims 的回退/默认创建逻辑
        # hidden_dims 现在是必需的 List[int]
        # if self.hidden_dims is None or not self.hidden_dims:
        #     self.hidden_dims = [self.hidden_dim] * self.num_layers

        # 添加对 hidden_dims 内容的验证
        if not self.hidden_dims: # 检查列表是否为空
             raise ValueError("hidden_dims 列表不能为空")
        for dim in self.hidden_dims:
             if not isinstance(dim, int) or dim <= 0:
                  raise ValueError(f"hidden_dims 必须包含正整数, 得到 {dim}")
        # 验证列表长度是否与 num_layers 匹配 (可选，取决于设计意图)
        # if len(self.hidden_dims) != self.num_layers:
        #      raise ValueError(f"hidden_dims 列表长度 ({len(self.hidden_dims)}) 与 num_layers ({self.num_layers}) 不匹配")
