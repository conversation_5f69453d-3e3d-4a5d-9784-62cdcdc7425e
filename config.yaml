model:
  activation: leaky_relu
  dimensions:
    base_dim: 128  # 第一批更新：从48提升到128，对齐最佳架构
  sequence_strategy:
    padding: same
    stride: 8
    type: sliding_window
    window_size: 48
  discriminator:
    hidden_dim: 128  # 第一批更新：从96提升到128，对齐最佳架构
    num_layers: 4   # 保持4层，后续批次再调整到6层
    enable_dynamic_fusion: false              # 关闭动态特征融合
    enable_temporal_attention: false          # 关闭时序多头注意力
    enable_adaptive_dilation_attention: false # 关闭自适应扩张率注意力
    enable_multiscale_convolution_attention: false # 关闭多尺度卷积注意力
    learning_rate: 0.0002
    beta1: 0.5
    beta2: 0.999
    hidden_dims: [128, 96, 64, 48]  # 第一批更新：调整层级维度对齐新的hidden_dim
    num_attention_heads: 8  # 第一批更新：从2提升到8，对齐最佳架构
    attention_types: []  # 清空注意力类型
    use_spectral_norm: true
    weight_sharing: false
    branch_config:
      trend_branch: false    # 关闭趋势一致性分支
      feature_branch: false  # 关闭特征关联分支
      temporal_branch: false # 关闭时序模式分支
  dropout_rate: 0.203  # 第一批更新：从0.297调整到0.203，对齐最佳架构
  generator:
    num_layers: 2  # 保持2层，与最佳架构一致
    activation_type: tanh
    use_layer_norm: true
    use_residual: true
    noise_injection_layers: [1]
  generator_type: lstm
  hidden_dim: 128  # 第一批更新：从48提升到128，对齐最佳架构
  loss:
    feature_matching_weight: 2.7  # 第二批更新：从1.0提升到2.7，对齐最佳架构
    temporal_consistency_weight: 0.15  # 第二批更新：从0.1提升到0.15，对齐最佳架构
    adversarial_weight: 0.525  # 第二批更新：从1.0降低到0.525，对齐最佳架构
    regression_weight: 7.5  # 第二批更新：添加回归损失权重，对齐最佳架构
    label_smoothing: 0.25  # 第二批更新：添加标签平滑，对齐最佳架构
  loss_type: mse
  n_heads: 2  # 保持2头
  noise:
    dim: 128  # 第一批更新：从48提升到128，对齐最佳架构
    distribution: normal
    scale: 1.0
    seed: 42
    dtype: float32
    structured: true
    temporal_correlation: 0.5
    feature_correlation: 0.3
    noise_patterns: ["temporal", "feature", "pattern"]
  noise_dim: 128  # 第一批更新：从48提升到128，对齐最佳架构
  num_layers: 2
  type: gan
  use_batch_norm: true
  use_spectral_norm: true
  attention:
    dropout: 0.203  # 第一批更新：对齐generator dropout率
    num_heads: 8  # 第一批更新：从2提升到8，对齐最佳架构
    num_scales: 2  # 保持2尺度
    dilation_rates: [4]  # 保持单个值
    multi_head_num_heads: 8  # 第一批更新：从2提升到8
    multi_head_dropout: 0.203  # 第一批更新：对齐dropout率
    multi_scale_num_heads: 8  # 第一批更新：从2提升到8
    multi_scale_num_scales: 1
    multi_scale_dropout: 0.203  # 第一批更新：对齐dropout率
    multi_scale_dilation_rates: [4]  # 保持单个值
    temporal_wrapper_dropout: 0.203  # 第一批更新：对齐dropout率
    adaptive_attention_num_scales: 2  # 保持2尺度
    adaptive_attention_dropout: 0.203  # 第一批更新：对齐dropout率
    adaptive_attention_num_heads: 8  # 第一批更新：从2提升到8
  feature_extractor:
    msfe_num_scales: 2  # 保持2尺度
    msfe_dropout: 0.203  # 第一批更新：对齐dropout率
    msfe_kernel_sizes: [3, 7]  # 从[3,9]调整到[3,7] (协调减少)
    tsfe_num_layers: 3  # 从4减少到3 (协调减少25%)
    tsfe_dropout: 0.203  # 第一批更新：对齐dropout率
    tsfe_hidden_dim: 128  # 第一批更新：从48提升到128，对齐最佳架构
    msfe_input_dim: 128  # 第一批更新：从48提升到128，对齐最佳架构
    msfe_hidden_dim: 128  # 第一批更新：从48提升到128，对齐最佳架构
    tsfe_input_dim: 128  # 第一批更新：从48提升到128，对齐最佳架构
    tsfe_output_dim: 128  # 第一批更新：从48提升到128，对齐最佳架构
    temporal_window_sizes: [72, 24]  # 从[96,24]调整到[72,24] (协调减少)
    amplification_factor: 2.1896141518880565  # 保持原值
    use_causal_attention: true
    feature_fusion_type: "attention"

data:
  batch_size: 128
  columns:
    temporal:
    - date
  data_path: data/raw/combined_data.csv
  drop_last: false
  feature_dim: null
  feature_selection:
    enable: true
  num_workers: 0
  pin_memory: true
  prediction_horizon: 1
  preprocessing:
    cleaning:
      missing_value_strategy: median
      outlier_threshold: 3.0
      pre_window_size: 5
      post_window_size: 5
      level_threshold: 0.1
      volatility_threshold: 2.0
      trend_angle_threshold: 30.0
      invalid_value_handling:
        interpolation_window_size: 5
        local_median_window_size: 5
        max_interpolation_attempts: 3
      outlier_handling:
        rolling_window_size: 20
        rolling_median_window_size: 5
        log_details: true
    padding_mode: replicate
    preserve_dims: true
    price_jump_detection:
      enable: true
      window:
        forward_size: 5
        backward_size: 5
      thresholds:
        base_threshold: 0.95
        significance_boost: 1.1
        min_significance_score: 0.85
        min_percent_change: 0.12
        price_change: 0.2
        volatility_ratio: 2.5
        trend_angle: 30.0
      weights:
        level: 0.5
        volatility: 0.3
        trend: 0.2
      dynamic_adjustment:
        base_factor: 1.8
        length_adjustment:
          long_series_threshold: 1000
          long_series_factor: 1.1
          short_series_threshold: 100
          short_series_factor: 0.9
        volatility_adjustment:
          high_threshold: 0.2
          high_factor: 1.2
          low_threshold: 0.05
          low_factor: 1.0
        autocorr_adjustment:
          threshold: 0.7
          factor: 1.1
        trend_adjustment:
          threshold: 0.8
          factor: 1.1
        range_adjustment:
          threshold: 2.0
          factor: 1.1
      validation:
        max_jumps_percentage: 0.15
        min_jumps_allowed: 1
        min_percent_change: 0.12
    validation:
      enable: true
  shuffle: true
  stride: 8
  target: value15
  test_ratio: 0.15
  train_ratio: 0.7
  val_ratio: 0.15
  window_size: 48
  model_path: outputs/models/gan_model.pt
  sequence_strategy:
    padding: same
    stride: 8
    type: sliding_window
    window_size: 48
  load_period: '2020-01-01/2099-12-31'

evaluation:
  batch_size: 128
  max_value: 1e6
  metrics: ["mae"]  # 保留最小评估指标以满足验证要求
  min_variance: 1e-6
  prediction:
    batch_size: 128
    enabled: false  # 关闭预测评估
    output_dir: outputs/predictions
    save_format: csv

feature_engineering:
  layers:
    - level: 0
      generators: ["base_features"]
      keep_input_features: true
    - level: 1
      generators: ["time_series_features"]
      keep_input_features: true
    - level: 2
      generators: ["interaction"]
      keep_input_features: true
  keep_original_in_final: true
  interlayer_quality_filter:
    enable: true
    variance_threshold: 0.01
    min_features_threshold: 5
  base_features:
    enable: true
    keep_original: true
  columns:
    categorical: []
    numeric: []
    time_features:
    - date
  enable: true
  frequency_features:
    enable: false
    fft: true
    spectral_density: false
  quality_control:
    drop_duplicates: true
    drop_na: true
    enable: true
    outlier_detection:
      enable: true
      method: iqr
      threshold: 1.5
  statistical_features:
    enable: true
    correlation: true
    covariance: false
    pca:
      enable: false
      n_components: null
    rolling_stats:
      enable: true
      stats: ['mean', 'std']
      window_sizes:
      - 3
      - 7
      - 21
  time_series_features:
    diff_features:
      enable: true
      orders:
      - 1
      - 2
    enable: true
    lag_features:
      enable: true
      max_lag: 10
      step: 1
    volatility_features:
      dist: normal
      enable: true
      model_type: GARCH
      p: 1
      q: 1
      scale_threshold: 5000
    window_features:
      enable: true
      stats:
      - mean
      - min
      - max
      - median
      window_sizes:
      - 3
      - 12
      - 24
  time_preprocessing:
    features_to_extract:
      - 'year'
      - 'month'
      - 'day'
      - 'dayofweek'
      - 'dayofyear'
      - 'weekofyear'
      - 'quarter'
      - 'is_weekend'
      - 'month_sin'
      - 'month_cos'
      - 'dayofweek_sin'
      - 'dayofweek_cos'
  interaction_features:
    enable: true
    top_k: 5
    candidate_selection:
      enable: true
      methods: ['lag_corr', 'mutual_info']
      combination_logic: 'union'
      top_n_final_candidates: 10
      lag_corr:
        enable: true
        max_lag: 3
        abs_corr_threshold: 0.01
      mutual_info:
        enable: true
        mi_threshold: 0.01

feature_selection:
  enable: true
  method: "default_multi_stage"
  importance_threshold: 0.01
  lagged_corr:
    min_abs_corr: 0.1
    max_lag: 28
  noise_detection:
    low_variance_threshold: 0.01
    high_correlation_threshold: 0.95

logging:
  save_history: true
  performance_monitoring:
    cuda_tracking: true
    enable: true
    memory_tracking: true
  console:
    enabled: true
    level: DEBUG
  level: DEBUG
  date_format: '%Y-%m-%d %H:%M:%S'
  file:
    backup_count: 5
    max_size: 10485760
    path: logs/app.log
  format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
  handlers:
    console:
      enabled: true
      level: DEBUG
    file:
      enabled: true
      level: DEBUG
  metrics:
  - loss
  - val_loss
  - mse
  - mae
  - rmse
  module_levels:
    src.data.FeatureSelector: DEBUG
    src.data.data_pipeline: DEBUG
    src.data.feature_channel: DEBUG
    src.data.feature_engineer: DEBUG
    src.data.preprocessing.data_cleaner: DEBUG
    src.data.standardization: DEBUG
    src.models.base: DEBUG
    src.models.gan: DEBUG
    src.utils.monitor_manager: DEBUG
    src.utils.resource_manager: DEBUG
    src.utils.config.loader: DEBUG

monitoring:
  enable_model_stats: true  # 启用模型统计监控
  enable_error_monitor: true  # 启用错误监控
  stats_log_frequency: 10
  stats_detailed_frequency: 100
  stats_log_path: logs/model_stats.log
  track_weights: false  # 关闭权重跟踪
  track_gradients: false  # 关闭梯度跟踪
  track_activations: false
  nan_detection: false  # 关闭NaN检测
  error_log_path: logs/error_monitor.log
  enable_nan_detection_in_error_monitor: false  # 关闭错误监控中的NaN检测
  enable_traceback: false  # 关闭错误回溯
  max_errors: 5
  error_cooldown: 60

paths:
  checkpoint_dir: outputs/checkpoints
  data_dir: data
  logs_dir: logs
  model_dir: outputs/models
  model_files:
    discriminator: discriminator.pt
    generator: generator.pt
  raw_data: data/raw/combined_data.csv
  results_dir: outputs/results

prediction:
  batch_size: 128
  confidence_level: 0.95
  device: ${system.device}
  num_workers: 0
  return_confidence: true
  use_cache: true
  n_samples: 100

system:
  cache:
    enable_memory_cache: true
    size: 1000
  cuda:
    device_id: 0
    enable_memory_cache: true
    enabled: true
    memory_fraction: 0.95
    streams:
      adaptive:
        adjustment_step: 2
        enabled: true
        high_threshold: 70
        history_size: 2
        low_threshold: 50
        max_streams: 32
        min_streams: 4
        monitoring_interval: 1
      enable_monitoring: true
      max_streams: 32
      monitoring_interval: 1.0
  device: cuda
  memory:
    memory_limit: 0.95
    monitoring_interval: 60

training:
  batch_size: 128
  num_epochs: 100
  lambda_gp: 0.6  # 第二批更新：从0.1提升到0.6，对齐最佳架构
  use_adaptive_lambda_gp: true
  num_workers: 0
  dropout_rate: 0.1
  seed: 42
  save_dir: outputs/models
  optimizer:
    type: adamw  # 第一批更新：从adam切换到adamw，对齐最佳架构
    generator_lr: 0.003  # 第二批更新：从0.001进一步提升到0.003（渐进式提升，最终目标0.006）
    discriminator_lr: 0.002  # 第二批更新：从0.0008进一步提升到0.002（渐进式提升，最终目标0.0046）
    weight_decay: 0.0007  # 第一批更新：从0.0添加权重衰减，对齐最佳架构
    beta1: 0.4  # 第二批更新：从0.5调整到0.4（渐进式调整，最终目标0.315）
    beta2: 0.98  # 第二批更新：从0.999调整到0.98（渐进式调整，最终目标0.955）
    momentum: 0.9
    nesterov: false
    eps: 1e-8
  lr_scheduler:
    enabled: true
    factor: 0.9
    patience: 3
    min_delta: 1e-4
    monitor: val_loss
  early_stopping:
    enabled: true
    patience: 3
    min_delta: 1e-4
    monitor: val_loss
    mode: "min"
  checkpoint:
    enable_checkpointing: true
    metric_name: "val_loss"
    metric_mode: "min"
    keep_best_k: 3
    save_freq: 1
    keep_last_n: 1
    memory_optimization: false
  batch_size_optimizer:
    enabled: true
    initial_batch_size: 128
    min_batch_size: 32
    max_batch_size: 256
    memory_utilization_target: 0.8
    strategy: hybrid
    adjustment_interval: 100
    growth_factor: 1.2
    shrink_factor: 0.8
    stability_threshold: 5
    warmup_steps: 100
    oom_recovery: true
    step_size: 32
    patience: 3
    monitor: val_loss
  lr_balancer:
    enabled: true
    type: "enhanced_multi_metric"
    target_ratio: 1.2
    sensitivity: 0.15
    min_lr: 0.0001
    max_lr: 0.004
    epsilon: 0.00000001
    performance_weight: 0.4
    stability_weight: 0.2
    loss_ratio_weight: 0.4
    history_window: 5
    improvement_threshold: 0.03
    stability_threshold: 0.1
    # 新增：调整模式配置
    adjustment_mode: "adaptive"  # "sync", "inverse", "adaptive"
    mode_switch_threshold: 0.3   # 自适应模式切换阈值

  dynamic_batch_size: true
  gradient_explosion_threshold: 5.0
  gradient_clip_val: 0.5
  adaptive_lambda_gp:
    enabled: true
    base_lambda_gp: 0.6  # 第二批更新：从0.1提升到0.6，对齐最佳架构
    min_lambda_gp: 0.05
    max_lambda_gp: 1.0
    adaptation_rate: 0.12
    warmup_steps: 5
    update_interval: 2
    smoothing_factor: 0.7
    grad_norm_target: 1.0
    grad_norm_tolerance: 0.25
    verbose_logging: true
  balance:
    lower_threshold: 0.4
    upper_threshold: 1.0
    min_n_critic: 1
    max_n_critic: 5  # 性能优化: 限制最大判别器训练步数为3
    min_g_steps: 1
    max_g_steps: 5
  mixed_precision:
    enabled: false
    dtype: "float16"
    init_scale: 65536.0
    growth_factor: 2.0
    backoff_factor: 0.5
    growth_interval: 2000
    cast_model_outputs: false

optimization:
  fast_mode:
    num_epochs: 1
    batch_size: 128
    log_level: INFO
  parameter_exploration:
    start_date: '2023-06-01'
    end_date: '2024-09-30'

version: 2.0.0

preprocessing:
  enable: true
  standardization:
    method: standard
    feature_wise: true
  normalization:
    method: minmax
    feature_range: [0, 1]
